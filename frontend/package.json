{"name": "llm-rag-frontend", "version": "0.1.0", "private": true, "type": "module", "description": "Frontend interface for the LLM RAG Codebase Query System", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "vitest --run", "test:watch": "vitest --watch", "test:ui": "vitest --ui", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@radix-ui/react-slot": "^1.2.3", "@remixicon/react": "^4.6.0", "@tailwindcss/postcss": "^4.1.11", "@tanstack/react-query": "^5.84.2", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.539.0", "next": "^15.4.6", "next-themes": "^0.4.6", "postcss": "^8.5.6", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hook-form": "^7.62.0", "react-markdown": "^10.1.0", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "zod": "^4.0.17"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.31.0", "@ianvs/prettier-plugin-sort-imports": "^4.5.1", "@next/eslint-plugin-next": "^15.4.4", "@tanstack/react-query-devtools": "^5.84.2", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^24.2.1", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@typescript-eslint/eslint-plugin": "^8.39.0", "@typescript-eslint/parser": "^8.39.0", "@vitejs/plugin-react": "^5.0.0", "@vitest/browser": "^3.2.4", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "3.2.4", "eslint": "^9.33.0", "eslint-config-next": "^15.4.6", "jsdom": "^26.1.0", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "typescript": "^5.9.2", "typescript-eslint": "^8.38.0", "vitest": "^3.2.4"}, "prettier": {"endOfLine": "lf", "semi": false, "singleQuote": false, "tabWidth": 2, "trailingComma": "es5", "importOrder": ["^react$", "^next$", "", "<TYPES>^(node:)", "<TYPES>", "<TYPES>^[.]", "", "<BUILTIN_MODULES>", "<THIRD_PARTY_MODULES>", "", "^@/config/(.*)$", "^@/lib/(.*)$", "^@/hooks/(.*)$", "", "^@/components/ui/(.*)$", "^@/components/(.*)$", "^@/styles/(.*)$", "", "^@/app/(.*)$", "", "^[./]", "^(?!.*[.]css$)[./].*$", ".css$"], "overrides": [{"files": "**/*.test.ts", "options": {"importOrder": ["^vitest", "<THIRD_PARTY_MODULES>", "^[.]"]}}], "importOrderParserPlugins": ["typescript", "jsx", "decorators-legacy"], "plugins": ["prettier-oxc-parser", "@ianvs/prettier-plugin-sort-imports", "prettier-plugin-tailwindcss"], "importOrderTypeScriptVersion": "5.0.0", "importOrderCaseSensitive": false}, "engines": {"node": ">=20.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@9.0.0"}