/**
 * API Response Type Definitions
 * 
 * This module defines TypeScript interfaces for all API response payloads,
 * matching the backend Pydantic models exactly to ensure type safety
 * and proper handling of server responses.
 */

import type {
  ActiveSessionCount,
  AgentSystemStatus,
  AgentType,
  ApiStatus,
  ChunkCount,
  ConfidenceScore,
  EmbeddingCount,
  FileCount,
  IngestionStatus,
  ProcessingTime,
  RepositoryIdentifier,
  SessionId,
  SourceCitation,
  StructuredData,
} from "./common"

/**
 * Response payload from the query endpoint
 * 
 * Corresponds to the backend QueryResponse Pydantic model.
 * Contains the processed response from the multi-agent system.
 * 
 * @example
 * ```typescript
 * const queryResponse: QueryResponse = {
 *   result_markdown: "# Authentication System\n\nThe system uses JWT tokens...",
 *   structured: { "components": ["auth", "jwt"], "complexity": "medium" },
 *   agent_type: "TECHNICAL_ARCHITECT",
 *   confidence: 0.95,
 *   sources: ["src/auth/jwt.py:1-50", "docs/auth.md:10-30"],
 *   processing_time: 2.3,
 *   session_id: "session-123"
 * }
 * ```
 */
export interface QueryResponse {
  /**
   * Formatted response content in Markdown
   * Contains the main response with proper formatting for display
   * @example "# Analysis Results\n\n## Overview\nThe authentication system..."
   */
  result_markdown: string

  /**
   * Structured response data for programmatic access
   * Contains machine-readable data extracted from the analysis
   * @example { "files_analyzed": 5, "patterns_found": ["singleton", "factory"] }
   */
  structured: StructuredData

  /**
   * Primary agent that processed the query
   * Indicates which agent was responsible for the main response
   * @example "TECHNICAL_ARCHITECT"
   */
  agent_type: string

  /**
   * Confidence score of the response (0.0 to 1.0)
   * Higher values indicate more confident responses
   * @example 0.85
   */
  confidence: ConfidenceScore

  /**
   * Source file citations referenced in the response
   * Array of file references with optional line numbers
   * @example ["src/main.py:10-25", "docs/architecture.md:45-60"]
   */
  sources: SourceCitation[]

  /**
   * Processing time in seconds (optional)
   * Time taken to generate the response
   * @example 2.34
   */
  processing_time?: ProcessingTime

  /**
   * Session ID for conversation tracking
   * Links the response to the conversation session
   * @example "session-abc123"
   */
  session_id: SessionId
}

/**
 * Response payload from the ingestion endpoint
 * 
 * Corresponds to the backend IngestionResponse Pydantic model.
 * Contains metrics and status information about repository ingestion.
 * 
 * @example
 * ```typescript
 * const ingestionResponse: IngestionResponse = {
 *   status: "completed",
 *   repository: "https://github.com/owner/repo",
 *   processed_files: 150,
 *   chunks_created: 1200,
 *   embeddings_generated: 1200,
 *   processing_time: 45.7
 * }
 * ```
 */
export interface IngestionResponse {
  /**
   * Ingestion status
   * Indicates the current state of the ingestion process
   * @example "completed"
   */
  status: string

  /**
   * Repository that was ingested
   * The repository URL that was processed
   * @example "https://github.com/owner/repository"
   */
  repository: RepositoryIdentifier

  /**
   * Number of files processed during ingestion
   * Count of individual files that were analyzed
   * @example 150
   */
  processed_files: FileCount

  /**
   * Number of text chunks created
   * Count of document chunks generated for embedding
   * @example 1200
   */
  chunks_created: ChunkCount

  /**
   * Number of embeddings generated
   * Count of vector embeddings created for search
   * @example 1200
   */
  embeddings_generated: EmbeddingCount

  /**
   * Total processing time in seconds
   * Time taken to complete the entire ingestion process
   * @example 45.7
   */
  processing_time: ProcessingTime
}

/**
 * Response payload from the status endpoint
 *
 * Corresponds to the backend StatusResponse Pydantic model.
 * Contains health and operational status of all system components.
 *
 * @example
 * ```typescript
 * const statusResponse: StatusResponse = {
 *   api: "operational",
 *   agents: {
 *     "ORCHESTRATOR": "operational",
 *     "TECHNICAL_ARCHITECT": "operational",
 *     "TASK_PLANNER": "operational"
 *   },
 *   vector_store: "operational",
 *   ingestion_pipeline: "operational",
 *   active_sessions: 5
 * }
 * ```
 */
export interface StatusResponse {
  /**
   * API service status
   * Overall health status of the API service
   * @example "operational"
   */
  api: string

  /**
   * Agent system status mapping
   * Status of each individual agent in the system
   * @example { "ORCHESTRATOR": "operational", "TASK_PLANNER": "error: timeout" }
   */
  agents: AgentSystemStatus

  /**
   * Vector store status
   * Health status of the vector database
   * @example "operational"
   */
  vector_store: string

  /**
   * Ingestion pipeline status
   * Health status of the document ingestion system
   * @example "operational"
   */
  ingestion_pipeline: string

  /**
   * Number of active conversation sessions
   * Current count of active user sessions
   * @example 5
   */
  active_sessions: ActiveSessionCount
}
