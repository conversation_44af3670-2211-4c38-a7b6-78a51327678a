/**
 * API Types Barrel Export
 * 
 * This module provides a centralized export point for all API-related types,
 * enabling clean imports throughout the application and maintaining a clear
 * separation between API types and other application types.
 * 
 * @example
 * ```typescript
 * import { QueryRequest, QueryResponse, ApiStatus } from '@/types/api'
 * ```
 */

// Common types and enums
export type {
  ContextFilters,
  StructuredData,
  AgentSystemStatus,
  SourceCitation,
  SessionId,
  UserId,
  RepositoryIdentifier,
  BranchName,
  CommitSha,
  ConfidenceScore,
  ProcessingTime,
  FileCount,
  ChunkCount,
  EmbeddingCount,
  ActiveSessionCount,
} from "./common"

export {
  AgentType,
  ApiStatus,
  IngestionStatus,
} from "./common"

// Request types
export type {
  QueryRequest,
  IngestionRequest,
} from "./requests"

// Response types
export type {
  QueryResponse,
  IngestionResponse,
  StatusResponse,
} from "./responses"
