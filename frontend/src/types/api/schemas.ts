/**
 * Zod Validation Schemas for API Types
 * 
 * This module provides runtime validation schemas using Zod for all API types,
 * ensuring type safety at runtime and enabling proper error handling for
 * malformed API responses.
 */

import { z } from "zod"

/**
 * Schema for agent types
 */
export const AgentTypeSchema = z.enum([
  "ORCHESTRATOR",
  "TECHNICAL_ARCHITECT", 
  "TASK_PLANNER",
  "RAG_RETRIEVAL",
])

/**
 * Schema for API status values
 */
export const ApiStatusSchema = z.enum([
  "operational",
  "degraded",
  "down", 
  "maintenance",
])

/**
 * Schema for ingestion status values
 */
export const IngestionStatusSchema = z.enum([
  "pending",
  "in_progress",
  "completed",
  "failed",
  "cancelled",
])

/**
 * Schema for context filters (flexible object)
 */
export const ContextFiltersSchema = z.record(z.string(), z.unknown()).optional()

/**
 * Schema for structured data (flexible object)
 */
export const StructuredDataSchema = z.record(z.string(), z.unknown())

/**
 * Schema for confidence scores (0.0 to 1.0)
 */
export const ConfidenceScoreSchema = z.number().min(0).max(1)

/**
 * Schema for source citations
 */
export const SourceCitationSchema = z.string()

/**
 * Schema for session IDs
 */
export const SessionIdSchema = z.string()

/**
 * Schema for user IDs
 */
export const UserIdSchema = z.string()

/**
 * Schema for repository identifiers
 */
export const RepositoryIdentifierSchema = z.string().url()

/**
 * Schema for branch names
 */
export const BranchNameSchema = z.string().min(1)

/**
 * Schema for commit SHAs
 */
export const CommitShaSchema = z.string().regex(/^[a-f0-9]{40}$/)

/**
 * Schema for processing times (positive numbers)
 */
export const ProcessingTimeSchema = z.number().positive()

/**
 * Schema for file counts (non-negative integers)
 */
export const FileCountSchema = z.number().int().min(0)

/**
 * Schema for chunk counts (non-negative integers)
 */
export const ChunkCountSchema = z.number().int().min(0)

/**
 * Schema for embedding counts (non-negative integers)
 */
export const EmbeddingCountSchema = z.number().int().min(0)

/**
 * Schema for active session counts (non-negative integers)
 */
export const ActiveSessionCountSchema = z.number().int().min(0)

/**
 * Schema for agent system status mapping
 */
export const AgentSystemStatusSchema = z.record(z.string(), z.string())

/**
 * Schema for QueryRequest
 */
export const QueryRequestSchema = z.object({
  query: z.string().min(1),
  session_id: SessionIdSchema.optional(),
  user_id: UserIdSchema.optional(),
  context_filters: ContextFiltersSchema,
  repository: RepositoryIdentifierSchema.optional(),
})

/**
 * Schema for IngestionRequest
 */
export const IngestionRequestSchema = z.object({
  repository_url: RepositoryIdentifierSchema,
  branch: BranchNameSchema.optional(),
  commit_sha: CommitShaSchema.optional(),
  force_refresh: z.boolean().optional(),
})

/**
 * Schema for QueryResponse
 */
export const QueryResponseSchema = z.object({
  result_markdown: z.string(),
  structured: StructuredDataSchema,
  agent_type: z.string(),
  confidence: ConfidenceScoreSchema,
  sources: z.array(SourceCitationSchema),
  processing_time: ProcessingTimeSchema.optional(),
  session_id: SessionIdSchema,
})

/**
 * Schema for IngestionResponse
 */
export const IngestionResponseSchema = z.object({
  status: z.string(),
  repository: RepositoryIdentifierSchema,
  processed_files: FileCountSchema,
  chunks_created: ChunkCountSchema,
  embeddings_generated: EmbeddingCountSchema,
  processing_time: ProcessingTimeSchema,
})

/**
 * Schema for StatusResponse
 */
export const StatusResponseSchema = z.object({
  api: z.string(),
  agents: AgentSystemStatusSchema,
  vector_store: z.string(),
  ingestion_pipeline: z.string(),
  active_sessions: ActiveSessionCountSchema,
})

/**
 * Type inference helpers
 */
export type QueryRequestType = z.infer<typeof QueryRequestSchema>
export type IngestionRequestType = z.infer<typeof IngestionRequestSchema>
export type QueryResponseType = z.infer<typeof QueryResponseSchema>
export type IngestionResponseType = z.infer<typeof IngestionResponseSchema>
export type StatusResponseType = z.infer<typeof StatusResponseSchema>
