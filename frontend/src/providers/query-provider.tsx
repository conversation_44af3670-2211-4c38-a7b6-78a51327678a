/**
 * React Query Provider
 * 
 * This module provides the React Query provider with optimized configuration
 * for the LLM RAG system, including intelligent caching strategies, error
 * handling, and performance optimizations.
 */

"use client"

import React from "react"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { ReactQueryDevtools } from "@tanstack/react-query-devtools"

import { config } from "@/lib/config"
import { useErrorHandler } from "@/hooks/useErrorHandler"

/**
 * Query client configuration optimized for the LLM RAG system
 */
function createQueryClient(): QueryClient {
  return new QueryClient({
    defaultOptions: {
      queries: {
        // Stale time - how long data is considered fresh
        staleTime: config.query.defaultStaleTime,
        
        // Cache time - how long data stays in cache after becoming stale
        gcTime: config.query.defaultCacheTime,
        
        // Retry configuration
        retry: (failureCount, error) => {
          // Don't retry on client errors (4xx)
          if (error && typeof error === "object" && "status" in error) {
            const status = (error as { status: number }).status
            if (status >= 400 && status < 500) {
              return false
            }
          }
          
          // Retry up to 3 times for other errors
          return failureCount < 3
        },
        
        // Retry delay with exponential backoff
        retryDelay: config.query.retryDelay,
        
        // Refetch on window focus for real-time data
        refetchOnWindowFocus: true,
        
        // Refetch on reconnect
        refetchOnReconnect: true,
        
        // Background refetching
        refetchInterval: false, // Disabled by default, enabled per query as needed
        
        // Network mode
        networkMode: "online",
        
        // Error handling
        throwOnError: false, // Handle errors in components
        
        // Placeholder data
        placeholderData: undefined,
        
        // Select function for data transformation
        select: undefined,
      },
      
      mutations: {
        // Retry configuration for mutations
        retry: (failureCount, error) => {
          // Don't retry mutations on client errors
          if (error && typeof error === "object" && "status" in error) {
            const status = (error as { status: number }).status
            if (status >= 400 && status < 500) {
              return false
            }
          }
          
          // Retry once for server errors
          return failureCount < 1
        },
        
        // Retry delay for mutations
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 5000),
        
        // Network mode
        networkMode: "online",
        
        // Error handling
        throwOnError: false,
      },
    },
    
    // Query cache configuration
    queryCache: undefined, // Use default
    
    // Mutation cache configuration  
    mutationCache: undefined, // Use default
    
    // Logger configuration
    logger: config.features.enableDebugMode
      ? {
          log: console.log,
          warn: console.warn,
          error: console.error,
        }
      : {
          log: () => {},
          warn: () => {},
          error: () => {},
        },
  })
}

/**
 * Query provider props
 */
interface QueryProviderProps {
  children: React.ReactNode
  client?: QueryClient
}

/**
 * React Query provider component
 * 
 * Provides React Query functionality to the entire application with
 * optimized configuration for the LLM RAG system.
 */
export function QueryProvider({ children, client }: QueryProviderProps) {
  // Create query client instance (memoized to prevent recreation)
  const queryClient = React.useMemo(() => client || createQueryClient(), [client])
  
  // Error handler for global query errors
  const { handleError } = useErrorHandler({
    showToast: true,
    logErrors: true,
    defaultContext: { component: "QueryProvider" },
  })
  
  // Set up global error handler
  React.useEffect(() => {
    const unsubscribe = queryClient.getQueryCache().subscribe((event) => {
      if (event.type === "error") {
        const { error, query } = event
        
        // Log query error with context
        console.error("React Query Error:", {
          error,
          queryKey: query.queryKey,
          queryHash: query.queryHash,
          state: query.state,
        })
        
        // Handle error through global error handler
        handleError(error as Error, {
          queryKey: query.queryKey,
          queryHash: query.queryHash,
        })
      }
    })
    
    return unsubscribe
  }, [queryClient, handleError])
  
  // Set up global mutation error handler
  React.useEffect(() => {
    const unsubscribe = queryClient.getMutationCache().subscribe((event) => {
      if (event.type === "error") {
        const { error, mutation } = event
        
        // Log mutation error with context
        console.error("React Query Mutation Error:", {
          error,
          mutationKey: mutation.options.mutationKey,
          variables: mutation.state.variables,
          context: mutation.state.context,
        })
        
        // Handle error through global error handler
        handleError(error as Error, {
          mutationKey: mutation.options.mutationKey,
          variables: mutation.state.variables,
        })
      }
    })
    
    return unsubscribe
  }, [queryClient, handleError])
  
  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {config.features.enableDebugMode && (
        <ReactQueryDevtools
          initialIsOpen={false}
          position="bottom-right"
          toggleButtonProps={{
            style: {
              position: "fixed",
              bottom: "20px",
              right: "20px",
              zIndex: 99999,
            },
          }}
        />
      )}
    </QueryClientProvider>
  )
}

/**
 * Hook to access the query client
 */
export function useQueryClient() {
  const client = React.useContext(QueryClientProvider as any)?.client
  
  if (!client) {
    throw new Error("useQueryClient must be used within a QueryProvider")
  }
  
  return client as QueryClient
}

/**
 * Query key factory for consistent key generation
 */
export const queryKeys = {
  // Query keys
  queries: {
    all: ["queries"] as const,
    bySession: (sessionId: string) => ["queries", "session", sessionId] as const,
    byUser: (userId: string) => ["queries", "user", userId] as const,
  },
  
  // Ingestion keys
  ingestion: {
    all: ["ingestion"] as const,
    byRepository: (repository: string) => ["ingestion", "repository", repository] as const,
    status: (repository: string) => ["ingestion", "status", repository] as const,
  },
  
  // Status keys
  status: {
    all: ["status"] as const,
    system: () => ["status", "system"] as const,
    health: () => ["status", "health"] as const,
    agents: () => ["status", "agents"] as const,
  },
  
  // Session keys
  sessions: {
    all: ["sessions"] as const,
    byUser: (userId: string) => ["sessions", "user", userId] as const,
    current: () => ["sessions", "current"] as const,
  },
} as const

/**
 * Query key type helpers
 */
export type QueryKeys = typeof queryKeys

/**
 * Utility function to invalidate related queries
 */
export function invalidateQueries(
  queryClient: QueryClient,
  patterns: {
    queries?: boolean
    ingestion?: boolean
    status?: boolean
    sessions?: boolean
  }
) {
  const promises: Promise<void>[] = []
  
  if (patterns.queries) {
    promises.push(queryClient.invalidateQueries({ queryKey: queryKeys.queries.all }))
  }
  
  if (patterns.ingestion) {
    promises.push(queryClient.invalidateQueries({ queryKey: queryKeys.ingestion.all }))
  }
  
  if (patterns.status) {
    promises.push(queryClient.invalidateQueries({ queryKey: queryKeys.status.all }))
  }
  
  if (patterns.sessions) {
    promises.push(queryClient.invalidateQueries({ queryKey: queryKeys.sessions.all }))
  }
  
  return Promise.all(promises)
}

/**
 * Default query client instance for use outside of React components
 */
export const defaultQueryClient = createQueryClient()
