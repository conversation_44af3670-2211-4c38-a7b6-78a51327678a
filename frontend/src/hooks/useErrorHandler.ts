/**
 * Error Handling Hook
 * 
 * This hook provides a centralized way to handle errors throughout the application,
 * including error reporting, user notifications, and recovery mechanisms.
 */

import { useCallback, useState } from "react"
import { useToast } from "@/hooks/useToast"
import {
  createApiError,
  createNetworkError,
  formatError<PERSON>orUser,
  isRetryableError,
  logError,
} from "@/utils/errorUtils"
import type { AppError, ErrorContext, ErrorHandler } from "@/types/errors"
import { ErrorCategory, ErrorSeverity } from "@/types/errors"

/**
 * Configuration options for error handling
 */
interface ErrorHandlerOptions {
  /**
   * Whether to show toast notifications for errors
   */
  showToast?: boolean

  /**
   * Whether to log errors to console/monitoring
   */
  logErrors?: boolean

  /**
   * Custom error handler function
   */
  onError?: ErrorHandler

  /**
   * Default context for error reporting
   */
  defaultContext?: Partial<ErrorContext>

  /**
   * Whether to enable automatic retry for retryable errors
   */
  enableRetry?: boolean

  /**
   * Maximum number of retry attempts
   */
  maxRetries?: number
}

/**
 * Return type for the useErrorHandler hook
 */
interface UseErrorHandlerReturn {
  /**
   * Current error state
   */
  error: AppError | null

  /**
   * Whether an error is currently being handled
   */
  isHandlingError: boolean

  /**
   * Handle an error with optional context
   */
  handleError: (error: unknown, context?: Partial<ErrorContext>) => void

  /**
   * Handle API errors specifically
   */
  handleApiError: (response: Response, url?: string, method?: string) => Promise<void>

  /**
   * Handle network errors
   */
  handleNetworkError: (error: unknown, retryFn?: () => Promise<void>) => void

  /**
   * Clear the current error
   */
  clearError: () => void

  /**
   * Retry the last failed operation if available
   */
  retry: () => Promise<void>

  /**
   * Whether retry is available
   */
  canRetry: boolean
}

/**
 * Custom hook for centralized error handling
 * 
 * @param options - Configuration options for error handling
 * @returns Error handling utilities and state
 * 
 * @example
 * ```tsx
 * const { handleError, handleApiError, clearError } = useErrorHandler({
 *   showToast: true,
 *   defaultContext: { component: "QueryForm" }
 * })
 * 
 * // Handle API errors
 * try {
 *   const response = await fetch('/api/query')
 *   if (!response.ok) {
 *     await handleApiError(response, '/api/query', 'POST')
 *   }
 * } catch (error) {
 *   handleError(error)
 * }
 * ```
 */
export function useErrorHandler(options: ErrorHandlerOptions = {}): UseErrorHandlerReturn {
  const {
    showToast = true,
    logErrors = true,
    onError,
    defaultContext,
    enableRetry = true,
    maxRetries = 3,
  } = options

  const { toast } = useToast()
  const [error, setError] = useState<AppError | null>(null)
  const [isHandlingError, setIsHandlingError] = useState(false)
  const [retryFn, setRetryFn] = useState<(() => Promise<void>) | null>(null)
  const [retryCount, setRetryCount] = useState(0)

  /**
   * Internal error processing function
   */
  const processError = useCallback(
    (appError: AppError, context?: Partial<ErrorContext>) => {
      const fullContext = { ...defaultContext, ...context }

      // Log error if enabled
      if (logErrors) {
        logError(appError, fullContext)
      }

      // Call custom error handler if provided
      if (onError) {
        onError(appError, fullContext)
      }

      // Show toast notification if enabled
      if (showToast) {
        const userMessage = formatErrorForUser(appError)
        toast({
          title: "Error",
          description: userMessage,
          variant: "destructive",
        })
      }

      // Update error state
      setError(appError)
    },
    [defaultContext, logErrors, onError, showToast, toast]
  )

  /**
   * Handle generic errors
   */
  const handleError = useCallback(
    (error: unknown, context?: Partial<ErrorContext>) => {
      setIsHandlingError(true)

      try {
        let appError: AppError

        if (error instanceof Error) {
          // Check if it's a network error
          if (error.name === "TypeError" && error.message.includes("fetch")) {
            appError = createNetworkError(
              "Network connection failed",
              "connection",
              true
            )
          } else {
            // Generic error
            appError = {
              message: error.message,
              category: ErrorCategory.UNKNOWN,
              severity: ErrorSeverity.MEDIUM,
              timestamp: new Date().toISOString(),
              stack: error.stack,
            }
          }
        } else if (typeof error === "string") {
          appError = {
            message: error,
            category: ErrorCategory.UNKNOWN,
            severity: ErrorSeverity.MEDIUM,
            timestamp: new Date().toISOString(),
          }
        } else {
          appError = {
            message: "An unknown error occurred",
            category: ErrorCategory.UNKNOWN,
            severity: ErrorSeverity.MEDIUM,
            timestamp: new Date().toISOString(),
          }
        }

        processError(appError, context)
      } finally {
        setIsHandlingError(false)
      }
    },
    [processError]
  )

  /**
   * Handle API errors from fetch responses
   */
  const handleApiError = useCallback(
    async (response: Response, url?: string, method?: string) => {
      setIsHandlingError(true)

      try {
        const apiError = await createApiError(response, url, method)
        processError(apiError, { context: { url, method } })
      } finally {
        setIsHandlingError(false)
      }
    },
    [processError]
  )

  /**
   * Handle network errors with optional retry
   */
  const handleNetworkError = useCallback(
    (error: unknown, retryFunction?: () => Promise<void>) => {
      const networkError = createNetworkError(
        error instanceof Error ? error.message : "Network error occurred",
        "connection",
        !!retryFunction,
        retryCount,
        maxRetries
      )

      processError(networkError)

      // Store retry function if provided and retry is enabled
      if (enableRetry && retryFunction && retryCount < maxRetries) {
        setRetryFn(() => retryFunction)
      }
    },
    [processError, enableRetry, retryCount, maxRetries]
  )

  /**
   * Clear the current error
   */
  const clearError = useCallback(() => {
    setError(null)
    setRetryFn(null)
    setRetryCount(0)
  }, [])

  /**
   * Retry the last failed operation
   */
  const retry = useCallback(async () => {
    if (!retryFn || !error || !isRetryableError(error)) {
      return
    }

    setIsHandlingError(true)
    setRetryCount((prev) => prev + 1)

    try {
      await retryFn()
      clearError()
    } catch (retryError) {
      handleError(retryError, { action: "retry", context: { retryAttempt: retryCount + 1 } })
    } finally {
      setIsHandlingError(false)
    }
  }, [retryFn, error, retryCount, clearError, handleError])

  /**
   * Check if retry is available
   */
  const canRetry = Boolean(
    retryFn && 
    error && 
    isRetryableError(error) && 
    retryCount < maxRetries
  )

  return {
    error,
    isHandlingError,
    handleError,
    handleApiError,
    handleNetworkError,
    clearError,
    retry,
    canRetry,
  }
}
